/* 收支管理统计卡片样式 - 与数据统计页面保持一致 */
.expense-statistic-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
  height: 100%;
  transition: all 0.3s ease;
}

.expense-statistic-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-4px);
}

.expense-statistic-card .ant-card-body {
  padding: 20px;
}

.expense-statistic-card-inner {
  display: flex;
  align-items: center;
}

.expense-statistic-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.expense-statistic-card-content {
  flex: 1;
}

.expense-statistic-card-title {
  color: #8c8c8c;
  font-size: 14px;
  margin-bottom: 8px;
  display: block;
}

.expense-statistic-card-growth {
  font-size: 14px;
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .expense-statistic-card .ant-card-body {
    padding: 16px;
  }
  
  .expense-statistic-card-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
    margin-right: 12px;
  }
}

@media (max-width: 768px) {
  .expense-statistic-card .ant-card-body {
    padding: 12px;
  }
  
  .expense-statistic-card-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
    margin-right: 10px;
  }
}
