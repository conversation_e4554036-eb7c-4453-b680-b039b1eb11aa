import React, { CSSProperties } from 'react';
import { Row, Col, Card, Statistic, Space } from 'antd';
import {
  DollarOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MoneyCollectOutlined,
  PayCircleOutlined,
  WalletOutlined,
  BankOutlined,
  ShoppingOutlined,
  BookOutlined,
  GiftOutlined
} from '@ant-design/icons';
import './ExpenseStatistics.css';

interface FinanceStatisticsProps {
  totalExpense: number;
  totalIncome: number;
  salaryExpense: number;
  operationExpense: number;
  otherExpense: number;
  tuitionIncome: number;
  trainingIncome: number;
  otherIncome: number;
}

// 统计卡片组件，与数据统计页面风格一致
const StatisticCard: React.FC<{
  icon: React.ReactNode;
  title: string;
  value: number;
  growth: number;
  color: string;
  prefix?: React.ReactNode;
  isTotal?: boolean;
}> = ({ icon, title, value, growth, color, prefix, isTotal = false }) => {
  const isUp = growth >= 0;

  const iconStyle: CSSProperties = {
    backgroundColor: `${color}20`,
    color: color,
    width: '48px',
    height: '48px',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: '16px',
    fontSize: '24px',
  };

  const cardClassName = isTotal
    ? "expense-statistic-card expense-statistic-card-total"
    : "expense-statistic-card";

  return (
    <Card className={cardClassName}>
      <div className="expense-statistic-card-inner">
        <div className="expense-statistic-card-icon" style={iconStyle}>
          {icon}
        </div>
        <div className="expense-statistic-card-content">
          <Statistic
            title={
              <span className="expense-statistic-card-title">
                {isTotal && <span style={{ fontSize: '12px', color: '#999', marginRight: '4px' }}>汇总</span>}
                {title}
              </span>
            }
            value={value}
            precision={2}
            prefix={prefix}
            valueStyle={{
              color: '#333',
              fontSize: isTotal ? '24px' : '20px',
              fontWeight: isTotal ? 700 : 600
            }}
            suffix={
              <Space size={4} className="expense-statistic-card-growth" style={{ color: isUp ? '#52c41a' : '#f5222d' }}>
                {isUp ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                <span>{Math.abs(growth)}%</span>
              </Space>
            }
          />
        </div>
      </div>
    </Card>
  );
};

const FinanceStatistics: React.FC<FinanceStatisticsProps> = ({
  totalExpense,
  totalIncome,
  salaryExpense,
  operationExpense,
  otherExpense,
  tuitionIncome,
  trainingIncome,
  otherIncome
}) => {
  // 模拟增长率数据（实际项目中应该从API获取）
  const statisticsData = [
    {
      icon: <MoneyCollectOutlined />,
      title: '总支出',
      value: totalExpense,
      growth: -3.2,
      color: '#f5222d',
      prefix: '¥',
    },
    {
      icon: <PayCircleOutlined />,
      title: '工资支出',
      value: salaryExpense,
      growth: 2.1,
      color: '#1890ff',
      prefix: '¥',
    },
    {
      icon: <BankOutlined />,
      title: '固定成本',
      value: operationExpense,
      growth: -1.5,
      color: '#faad14',
      prefix: '¥',
    },
    {
      icon: <WalletOutlined />,
      title: '其他支出',
      value: otherExpense,
      growth: -5.8,
      color: '#f5222d',
      prefix: '¥',
    },
    {
      icon: <DollarOutlined />,
      title: '总收入',
      value: totalIncome,
      growth: 8.5,
      color: '#52c41a',
      prefix: '¥',
    },
    {
      icon: <BookOutlined />,
      title: '学费收入',
      value: tuitionIncome,
      growth: 12.3,
      color: '#52c41a',
      prefix: '¥',
    },
    {
      icon: <ShoppingOutlined />,
      title: '培训收入',
      value: trainingIncome,
      growth: 6.7,
      color: '#13c2c2',
      prefix: '¥',
    },
    {
      icon: <GiftOutlined />,
      title: '其他收入',
      value: otherIncome,
      growth: 4.2,
      color: '#722ed1',
      prefix: '¥',
    },
  ];

  return (
    <div style={{ marginBottom: 24 }}>
      {/* 第一行：支出相关 */}
      <Row gutter={[12, 12]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={12} md={8} lg={7} key={0}>
          <StatisticCard {...statisticsData[0]} isTotal={true} />
        </Col>
        <Col xs={24} sm={12} md={16} lg={17}>
          <Row gutter={[8, 8]}>
            {statisticsData.slice(1, 4).map((stat, index) => (
              <Col xs={24} sm={24} md={8} lg={8} key={index + 1}>
                <StatisticCard {...stat} isTotal={false} />
              </Col>
            ))}
          </Row>
        </Col>
      </Row>

      {/* 第二行：收入相关 */}
      <Row gutter={[12, 12]}>
        <Col xs={24} sm={12} md={8} lg={7} key={4}>
          <StatisticCard {...statisticsData[4]} isTotal={true} />
        </Col>
        <Col xs={24} sm={12} md={16} lg={17}>
          <Row gutter={[8, 8]}>
            {statisticsData.slice(5, 8).map((stat, index) => (
              <Col xs={24} sm={24} md={8} lg={8} key={index + 5}>
                <StatisticCard {...stat} isTotal={false} />
              </Col>
            ))}
          </Row>
        </Col>
      </Row>
    </div>
  );
};

export default FinanceStatistics;